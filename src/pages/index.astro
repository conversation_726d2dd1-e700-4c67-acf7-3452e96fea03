---
import Layout from "../layouts/Layout.astro";
import RocketLaunchIcon from "astro-heroicons/outline/RocketLaunch.astro";
import CommandLineIcon from "astro-heroicons/outline/CommandLine.astro";
import CursorArrowRaysIcon from "astro-heroicons/outline/CursorArrowRays.astro";
import ChatBubbleLeftRightIcon from "astro-heroicons/outline/ChatBubbleLeftRight.astro";
import VariableIcon from "astro-heroicons/outline/Variable.astro";
import CreditCardIcon from "astro-heroicons/outline/CreditCard.astro";
const pageTitle = "FlowAI - AI 工作流构建与智能体构建平台";
const pageDescription =
	"FlowAI 是一个强大的AI工作流构建平台和AI智能体构建平台，无需编程经验即可创建自动化工作流和智能代理。支持多种大语言模型，提供直观的界面和丰富的功能，帮助您轻松实现工作流程自动化和智能体开发。";
const keywords =
	"AI工作流, AI智能体构建平台, 智能体开发, 自动化工具, 无代码平台, 大语言模型, 工作流自动化, FlowAI, 人工智能工作流, AI Agent, 智能代理, MCP服务器, 工具调用, 自主决策AI, 多步推理, 网页抓取, HTTP请求, 计算器工具, Model Context Protocol, AI智能体平台, 智能代理构建";

const features = [
	{
		icon: CommandLineIcon,
		title: "无需编程经验",
		desc: "我们的平台专为各种技能水平的用户设计，您无需具备任何编程知识即可创建和管理AI工作流。FlowAI 让每个人都能轻松上手，立即开始您的自动化之旅，释放您的创造力。",
		img: "/feature1.png",
	},
	{
		icon: CursorArrowRaysIcon,
		title: "AI 驱动的自动化",
		desc: "借助 FlowAI 的 AI 驱动功能，您可以自动化重复性任务，显著提高工作效率。将繁琐的工作交给智能系统，让您有更多时间专注于真正重要的事情，提升整体工作质量。",
		img: "/feature2.png",
	},
	{
		icon: VariableIcon,
		title: "多种工作流节点可用",
		desc: "FlowAI 提供多种工作流节点供您选择，您可以根据不同的任务和需求定制您的流程。无论是数据处理、信息传递还是其他任务，我们的平台都能满足您的多样化需求。",
		img: "/feature3.png",
	},
	{
		icon: ChatBubbleLeftRightIcon,
		title: "支持多种大语言模型",
		desc: "我们的平台集成了多种大语言模型（LLMs），为您的工作流提供灵活性和强大功能。无论您需要处理文本、生成内容还是进行复杂的分析，FlowAI 都能为您提供最佳解决方案。",
		img: "/feature4.png",
	},
	{
		icon: RocketLaunchIcon,
		title: "一键运行AI工作流",
		desc: "无需复杂操作，FlowAI 为每一个工作流生成一个可交互的UI界面，您只需点击按钮即可启动并运行您的流程，无需制作UI，无需编写代码。FlowAI 会自动为您处理所有细节，确保您的流程高效且可靠。",
		img: "/feature5.png",
	},
];

const faqs = [
	{
		question: "什么是 FlowAI？",
		answer: "FlowAI 是一个强大的工作流构建工具，旨在帮助用户利用人工智能（AI）技术来简化和自动化他们的工作流程。无论您是初学者还是有经验的用户，FlowAI 都能为您提供直观的界面和多种功能，帮助您轻松创建和管理工作流。",
	},
	{
		question: "我需要编程经验才能使用 FlowAI 吗？",
		answer: "不需要！FlowAI 设计得非常用户友好，适合各种技能水平的用户。您无需任何编程知识即可创建和管理工作流，您只需按照简单的步骤操作即可开始使用。当然，如果你编程经验，那你将更容易上手、更容易创建出复杂的工作流。",
	},
	{
		question: "FlowAI 如何帮助我提高工作效率？",
		answer: "FlowAI 利用 AI 的力量来自动化重复性任务，这样您就可以将时间和精力集中在更重要的事情上。通过自动化流程，您可以减少手动操作的时间，从而提高整体工作效率。",
	},
	{
		question: "我可以使用哪些类型的工作流节点？",
		answer: "FlowAI 提供多种工作流节点，您可以根据不同的任务和需求进行选择和定制。这些节点可以帮助您构建复杂的工作流，以满足您的特定要求。",
	},
	{
		question: "FlowAI 支持哪些大语言模型？",
		answer: "FlowAI 集成了多种大语言模型（LLMs）比如GPT 4o、Claude 3.5、DeepSeek，为用户提供灵活性和强大的功能。您可以根据自己的需求选择合适的模型，以实现最佳的工作流效果。",
	},
	{
		question: "如何开始使用 FlowAI？",
		answer: "要开始使用 FlowAI，您只需访问我们的官方网站，注册一个账户，然后登录到您的仪表板。接下来，您可以按照提示创建您的第一个工作流，体验 FlowAI 的强大功能。",
	},
	{
		question: "FlowAI 的定价是怎样的？",
		answer: "目前每个用户可以免费使用，注册就送50点的积分，积分使用完可以使用您自己的OpenAI API Key。对于具体的定价信息，请查看：https://flowai.cc/intro/price/",
	},
	{
		question: "FlowAI 是否提供客户支持？",
		answer: "是的，FlowAI 提供全面的客户支持。无论您在使用过程中遇到什么问题，都可以随时联系我们的支持团队，我们将竭诚为您提供帮助。",
	},
	{
		question: "我可以在 FlowAI 上创建多个工作流吗？",
		answer: "当然可以！FlowAI 允许用户创建和管理多个工作流，您可以根据不同的项目和需求创建不同的工作流。",
	},
	{
		question: "FlowAI 与其他工作流工具相比有什么优势？",
		answer: "目前市面上有很多工作流工具，诸如dify、豆包等。每个工具都有自己的优势，而且做AI工作流的门槛也不高，FlowAI 无意与他们竞争，每个用户都有自己的使用习惯，最流行的不一定是最适合的，用户可以多尝试，选择适合自己的工具。FlowAI 的定位是提供一个简单、易用、高效的AI工作流工具，让用户可以轻松创建和管理自己的工作流。",
	},
	{
		question: "什么是 FlowAI 的 AI Agent 功能？",
		answer: "FlowAI 的 AI Agent 是一个智能代理节点，它具备自主决策能力，能够根据任务需求自动调用各种工具来完成复杂任务。与普通的 LLM 节点只提供单次 AI 推理结果不同，AI Agent 可以进行多步推理，自动选择和使用合适的工具，如网页抓取、计算器、HTTP 请求等，从而完成更复杂的自动化流程。",
	},
	{
		question: "AI Agent 支持哪些类型的工具调用？",
		answer: "FlowAI 的 AI Agent 支持多种工具调用，包括但不限于：网页抓取工具（用于获取网页内容）、计算器工具（用于数学计算）、HTTP 请求工具（用于 API 调用）等。AI Agent 会根据任务需求智能选择合适的工具，并自动执行相应操作，大大提升了工作流的智能化程度。",
	},
	{
		question: "什么是 MCP 服务器？FlowAI 如何支持 MCP？",
		answer: "MCP（Model Context Protocol）是一种模型上下文协议，允许 AI 模型与外部服务进行标准化通信。FlowAI 的 AI Agent 节点现在完全支持 MCP 服务器配置，包括 stream HTTP 和 SSE（Server-Sent Events）类型。通过配置 MCP 服务器，AI Agent 可以自动使用 MCP 提供的各种功能和服务，极大扩展了 AI Agent 的能力边界。",
	},
	{
		question: "如何在 FlowAI 中配置和使用 MCP 服务器？",
		answer: "在 FlowAI 中配置 MCP 服务器非常简单：1）在 AI Agent 节点设置中找到 MCP 服务器配置选项；2）输入 MCP 服务器的地址和相关参数；3）选择连接类型（stream HTTP 或 SSE）；4）保存配置后，AI Agent 就能自动发现并使用该 MCP 服务器提供的所有工具和功能。这让您的工作流能够接入更多外部服务和能力。",
	},
	{
		question: "AI Agent 与普通 LLM 节点有什么区别？",
		answer: "主要区别在于智能程度和功能范围：1）普通 LLM 节点只进行单次 AI 推理，输入文本后直接输出结果；2）AI Agent 具备自主决策能力，可以进行多步推理和规划；3）AI Agent 能够自动调用工具，如网页抓取、API 请求等；4）AI Agent 支持 MCP 服务器集成，可以使用更多外部服务；5）AI Agent 更适合处理复杂的、需要多步操作的任务场景。",
	},
	{
		question: "使用 AI Agent 功能需要额外付费吗？",
		answer: "AI Agent 功能作为 FlowAI 平台的核心功能之一，包含在我们的标准服务中。您可以使用免费积分或自己的 API Key 来运行 AI Agent 工作流。由于 AI Agent 可能需要进行多次 AI 调用和工具使用，相比普通 LLM 节点可能会消耗更多积分，但这完全取决于任务的复杂程度和所需的操作步骤。",
	},
];

const ldJson = {
	"@context": "https://schema.org",
	"@type": "FAQPage",
	mainEntity: faqs.map((faq) => ({
		"@type": "Question",
		name: faq.question,
		acceptedAnswer: {
			"@type": "Answer",
			text: faq.answer,
		},
	})),
	hasPart: {
		"@type": "ItemList",
		itemListElement: features.map((feature, index) => ({
			"@type": "ListItem",
			position: index + 1,
			item: {
				"@type": "HowToTip",
				name: feature.title,
				text: feature.desc,
				image: feature.img,
			},
		})),
	},
};

const userLang = Astro.request.headers.get("accept-language") || "";
const isEnglish = userLang.startsWith("en");
---

<Layout title={pageTitle} description={pageDescription} keywords={keywords} jsonLd={ldJson}>
	{
		isEnglish && (
			<div class="bg-yellow-300 text-black p-4 text-center">
				<strong>Notice:</strong> You can switch to the{" "}
				<a href="/en" class="text-blue-600 underline" title="中文版">
					English version
				</a>
				.
			</div>
		)
	}
	<!-- <div
		class="bg-gradient-to-r from-blue-50 to-indigo-50 py-2 px-4 text-center text-sm border-b border-blue-100 shadow-sm mb-8"
	>
		<span class="inline-flex items-center">
			<span class="relative flex h-2 w-2 mr-2">
				<span
					class="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary opacity-75"
				></span>
				<span class="relative inline-flex rounded-full h-2 w-2 bg-primary"></span>
			</span>
			<span>FlowAI 已全面支持 <b>GPT 4.1</b> 模型</span>
		</span>
	</div> -->
	<div class="mt-40"></div>

	<div class="text-center ml-6 mr-6">
		<h1 class="text-6xl font-bold max-sm:text-4xl relative">
			<span
				class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
			>
				<span class="sr-only">FlowAI - </span>AI 工作流
			</span>
			从未如此简单
		</h1>
		<h2 class="text-3xl mt-5 max-sm:text-xl">
			使用 <span class="font-bold">FlowAI</span> 构建智能工作流和AI智能体
			<span class="font-bold">让AI赋能业务流程</span>
		</h2>

		<p class="mt-6 max-w-xl mx-auto text-lg text-gray-600">
			FlowAI是专业的 <span>AI工作流</span> 构建平台和 <span>AI智能体构建平台</span
			>，无需编程即可创建强大的自动化流程和智能代理，提升工作效率，释放创造力！
		</p>

		<a href="/dashboard" title="立即开始">
			<button class="btn btn-lg btn-primary mt-8 px-10">
				<RocketLaunchIcon class="w-5 h-5 mr-2" /> 立即免费开始
			</button>
		</a>
		<div class="flex items-center justify-center mt-2 text-sm italic">
			<CreditCardIcon class="w-4 h-4 mr-2" />
			无需信用卡/实名认证，注册赠送<span class="font-bold text-primary">50积分</span>
		</div>

		<p class="text-md mt-10">
			已有超过 <span class="font-bold">10,000+</span> 个<span class="text-primary"
				>AI工作流</span
			>被创建
		</p>
	</div>

	<div
		class="relative mix-blend-darken mt-20 mx-auto self-center bg-gradient-to-r from-indigo-400 to-cyan-400 max-w-[1300px] w-[90%] h-[900px] max-lg:h-[650px] max-md:w-[95%] max-md:h-[500px] max-sm:h-[400px] rounded-2xl flex justify-center box-border overflow-hidden"
	>
		<video
			src="/index-zh.mp4"
			class="w-full h-full object-cover"
			autoplay
			loop
			muted
			playsinline
			poster="/index-zh-poster.png"
		>
		</video>
	</div>

	<div class="mt-20 text-center self-center">
		<h2 class="text-3xl font-bold text-primary">
			轻松快速创建 <span class="font-normal">AI工作流</span>
		</h2>
		<p class="text-xl mt-2 max-w-3xl mx-auto">
			使用我们直观强大的工具，简化您的 <span
				class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
				>AI工作流</span
			> 创作流程，让自动化变得简单高效
		</p>
		<div
			class="flex flex-nowrap self-center max-w-[1500px] justify-start mt-10 w-screen overflow-x-scroll p-10 m-auto"
		>
			{
				features.map((feature) => (
					<div class="flex-none w-[500px] m-4 pt-6 bg-white rounded-xl overflow-hidden shadow-md text-left box-border">
						<div class="flex items-center mb-4 px-6">
							<feature.icon class="w-8 h-8 mr-2" />
							<h3 class="text-xl font-semibold">{feature.title}</h3>
						</div>
						<p class="mt-2 px-6">{feature.desc}</p>
						<img
							alt={feature.title}
							src={feature.img}
							class="w-full h-[300px] mt-5 object-cover"
						/>
					</div>
				))
			}
		</div>
	</div>

	<div class="mt-24 max-w-6xl mx-auto px-6">
		<div class="text-center mb-12">
			<h2 class="text-3xl font-bold text-primary">
				为什么选择 FlowAI 的 <span class="font-normal">AI工作流</span> 和 <span
					class="font-normal">AI智能体构建</span
				> 平台
			</h2>
			<p class="text-xl mt-3 max-w-3xl mx-auto">
				我们的平台为各类用户提供了创建专业 <span
					class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
					>AI工作流</span
				> 和 <span
					class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
					>AI智能体</span
				> 的最佳解决方案
			</p>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-2 gap-8">
			<div
				class="bg-gradient-to-br from-blue-50 to-indigo-50 p-8 rounded-xl shadow-sm border border-blue-100"
			>
				<h3 class="text-xl font-bold mb-4 text-primary">
					企业级 <span class="font-normal">AI工作流</span> 和 <span
						class="font-normal">AI智能体</span
					> 解决方案
				</h3>
				<p class="text-gray-700">
					FlowAI为企业提供强大的 <span
						class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
						>AI工作流</span
					> 和 <span
						class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
						>AI智能体</span
					> 工具，帮助自动化重复性任务，构建智能代理系统，提高团队效率。我们的平台支持多种大语言模型，可根据您的业务需求定制最佳解决方案。
				</p>
			</div>
			<div
				class="bg-gradient-to-br from-purple-50 to-pink-50 p-8 rounded-xl shadow-sm border border-purple-100"
			>
				<h3 class="text-xl font-bold mb-4 text-primary">
					个人创作者的 <span class="font-normal">AI工作流</span> 和 <span
						class="font-normal">AI智能体</span
					> 助手
				</h3>
				<p class="text-gray-700">
					对于个人创作者，FlowAI的 <span
						class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
						>AI工作流</span
					> 和 <span
						class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
						>AI智能体构建</span
					> 平台提供了简单易用的工具，帮助您自动化内容创作、数据处理等任务，构建个人AI助手，让您专注于创意而非重复性工作。
				</p>
			</div>
		</div>
	</div>

	<div class="mt-24 text-left self-center w-full max-w-4xl mx-auto px-4">
		<h2 class="text-3xl font-bold text-primary text-center">常见问题</h2>
		<p class="text-center text-lg mt-3 mb-10">
			了解更多关于 FlowAI <span
				class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
				>AI工作流</span
			> 平台的信息
		</p>
		<div class="mt-10">
			{
				faqs.map((faq) => (
					<div class="mb-6">
						<details class="group w-full">
							<summary class="flex justify-between items-center font-medium cursor-pointer list-none w-full">
								<h3 class="text-lg text-left flex-grow">{faq.question}</h3>
								<span class="transition group-open:rotate-180 flex-shrink-0 ml-2">
									<svg
										fill="none"
										height="24"
										shape-rendering="geometricPrecision"
										stroke="currentColor"
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="1.5"
										viewBox="0 0 24 24"
										width="24"
									>
										<path d="M6 9l6 6 6-6" />
									</svg>
								</span>
							</summary>
							<p class="text-neutral-600 mt-3 group-open:animate-fadeIn">
								{faq.answer}
							</p>
						</details>
					</div>
				))
			}
		</div>
	</div>

	<div class="mt-24 relative overflow-hidden">
		<!-- 背景装饰 -->
		<div class="absolute inset-0 bg-gradient-to-br from-blue-50 via-cyan-50 to-blue-100">
		</div>
		<div class="absolute inset-0 opacity-30">
			<div
				class="absolute top-0 left-0 w-96 h-96 rounded-full transform -translate-x-1/2 -translate-y-1/2 radial-gradient-blue"
			>
			</div>
			<div
				class="absolute bottom-0 right-0 w-96 h-96 rounded-full transform translate-x-1/2 translate-y-1/2 radial-gradient-cyan"
			>
			</div>
		</div>

		<!-- 装饰性几何图形 -->
		<div
			class="absolute top-10 left-10 w-20 h-20 bg-blue-300/40 rounded-full filter blur-xl animate-custom-pulse"
		>
		</div>
		<div
			class="absolute bottom-10 right-10 w-32 h-32 bg-cyan-300/40 rounded-full filter blur-xl animate-float"
		>
		</div>
		<div class="absolute top-1/2 left-1/4 w-3 h-3 bg-blue-500 rounded-full animate-ping">
		</div>
		<div
			class="absolute top-1/3 right-1/3 w-2 h-2 bg-cyan-500 rounded-full animate-ping"
			style="animation-delay: 2s;"
		>
		</div>
		<div
			class="absolute top-2/3 left-1/3 w-1 h-1 bg-blue-400 rounded-full animate-pulse"
			style="animation-delay: 3s;"
		>
		</div>
		<!-- 额外的装饰元素 -->
		<div
			class="absolute top-1/4 right-1/4 w-4 h-4 border-2 border-blue-300/50 rounded-full animate-spin"
			style="animation-duration: 8s;"
		>
		</div>
		<div
			class="absolute bottom-1/4 left-1/5 w-6 h-6 border border-cyan-300/50 rounded-full animate-bounce"
			style="animation-delay: 1s; animation-duration: 4s;"
		>
		</div>

		<div class="relative py-20 px-6">
			<div class="max-w-4xl mx-auto text-center">
				<!-- 主标题 -->
				<div class="mb-8">
					<h2 class="text-4xl md:text-5xl font-bold text-primary mb-4 leading-tight">
						开始使用 FlowAI 构建您的
						<span
							class="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative"
						>
							AI工作流
						</span>
					</h2>
					<div
						class="w-24 h-1 bg-gradient-to-r from-primary to-secondary mx-auto rounded-full"
					>
					</div>
				</div>

				<!-- 描述文字 -->
				<p class="text-xl mb-10 max-w-3xl mx-auto text-gray-700 leading-relaxed">
					FlowAI让 <span class="font-semibold text-primary">AI工作流</span> 构建变得简单高效。
					无论您是企业用户还是个人创作者，都能通过我们的平台轻松创建专业的
					<span class="font-semibold text-primary">AI工作流</span>。 立即注册，体验
					FlowAI 强大的功能！
				</p>

				<!-- Logo和统计信息 -->
				<div class="mb-10">
					<img
						src="/main-logo-no-bg.png"
						class="block w-32 mx-auto mb-4"
						alt="FlowAI"
					/>
					<div class="flex flex-wrap justify-center gap-8 text-sm text-gray-600">
						<div class="flex items-center">
							<div class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse">
							</div>
							<span>10,000+ 工作流已创建</span>
						</div>
						<div class="flex items-center">
							<div
								class="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse delay-500"
							>
							</div>
							<span>支持多种AI模型</span>
						</div>
						<div class="flex items-center">
							<div
								class="w-2 h-2 bg-purple-500 rounded-full mr-2 animate-pulse delay-1000"
							>
							</div>
							<span>免费注册赠送积分</span>
						</div>
					</div>
				</div>

				<!-- CTA按钮组 -->
				<div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
					<a href="/dashboard" title="现在就开始！" class="group">
						<button
							class="btn btn-primary btn-lg px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
						>
							<RocketLaunchIcon
								class="w-6 h-6 mr-2 group-hover:animate-bounce"
							/>
							立即开始构建AI工作流
						</button>
					</a>
					<a href="/intro/overview/" title="了解更多" class="group">
						<button
							class="btn btn-outline btn-primary btn-lg px-8 py-4 text-lg font-semibold hover:bg-primary hover:text-white transition-all duration-300"
						>
							<svg
								class="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
								></path>
							</svg>
							了解更多
						</button>
					</a>
				</div>

				<!-- 信任指标 -->
				<div class="mt-12 pt-8 border-t border-gray-200">
					<p class="text-sm text-gray-500 mb-4">受到全球用户信赖</p>
					<div class="flex justify-center items-center space-x-8 opacity-60">
						<div class="text-xs text-gray-400">🚀 快速部署</div>
						<div class="text-xs text-gray-400">🔒 安全可靠</div>
						<div class="text-xs text-gray-400">⚡ 高效自动化</div>
						<div class="text-xs text-gray-400">🎯 精准智能</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</Layout>

<style>
	details summary::-webkit-details-marker {
		display: none;
	}

	/* 自定义径向渐变背景 */
	.radial-gradient-blue {
		background: radial-gradient(
			circle,
			rgba(59, 130, 246, 0.15) 0%,
			rgba(59, 130, 246, 0.05) 50%,
			transparent 100%
		);
	}

	.radial-gradient-cyan {
		background: radial-gradient(
			circle,
			rgba(34, 211, 238, 0.15) 0%,
			rgba(34, 211, 238, 0.05) 50%,
			transparent 100%
		);
	}

	/* 增强动画效果 */
	@keyframes float {
		0%,
		100% {
			transform: translateY(0px);
		}
		50% {
			transform: translateY(-10px);
		}
	}

	.animate-float {
		animation: float 6s ease-in-out infinite;
	}

	/* 自定义脉冲动画 */
	@keyframes custom-pulse {
		0%,
		100% {
			opacity: 1;
			transform: scale(1);
		}
		50% {
			opacity: 0.5;
			transform: scale(1.1);
		}
	}

	.animate-custom-pulse {
		animation: custom-pulse 3s ease-in-out infinite;
	}
</style>
