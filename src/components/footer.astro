---
// 使用简单的SVG图标而不是外部图标库
---

<div class="max-w-6xl mx-auto px-6">
	<!-- 主要内容区域 -->
	<div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
		<!-- 产品信息 -->
		<div class="col-span-1 md:col-span-2">
			<div class="flex items-center mb-4">
				<img src="/main-logo-no-bg.png" class="w-8 h-8 mr-2" alt="FlowAI Logo" />
				<h3 class="text-lg font-bold text-gray-800">FlowAI</h3>
			</div>
			<p class="text-gray-600 text-sm mb-4 max-w-md">
				专业的AI工作流构建平台和AI智能体构建平台，让每个人都能轻松创建强大的自动化流程和智能代理。
			</p>
			<div class="flex space-x-4">
				<a
					href="https://twitter.com/flowai_cc"
					class="text-gray-500 hover:text-primary transition-colors"
					title="Twitter"
				>
					<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
						<path
							d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"
						></path>
					</svg>
				</a>
				<a
					href="https://github.com/flowai-cc"
					class="text-gray-500 hover:text-primary transition-colors"
					title="GitHub"
				>
					<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
						<path
							d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"
						></path>
					</svg>
				</a>
				<a
					href="mailto:<EMAIL>"
					class="text-gray-500 hover:text-primary transition-colors"
					title="邮箱"
				>
					<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
						<path
							d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"
						></path>
					</svg>
				</a>
			</div>
		</div>

		<!-- 快速链接 -->
		<div>
			<h4 class="font-semibold text-gray-800 mb-4">快速链接</h4>
			<ul class="space-y-2 text-sm">
				<li>
					<a
						href="/dashboard"
						class="text-gray-600 hover:text-primary transition-colors">开始使用</a
					>
				</li>
				<li>
					<a
						href="/intro/price/"
						class="text-gray-600 hover:text-primary transition-colors">定价方案</a
					>
				</li>
				<li>
					<a href="/docs" class="text-gray-600 hover:text-primary transition-colors"
						>使用文档</a
					>
				</li>
				<li>
					<a
						href="/examples"
						class="text-gray-600 hover:text-primary transition-colors">示例模板</a
					>
				</li>
			</ul>
		</div>

		<!-- 支持与帮助 -->
		<div>
			<h4 class="font-semibold text-gray-800 mb-4">支持与帮助</h4>
			<ul class="space-y-2 text-sm">
				<li>
					<a href="/help" class="text-gray-600 hover:text-primary transition-colors"
						>帮助中心</a
					>
				</li>
				<li>
					<a
						href="/contact"
						class="text-gray-600 hover:text-primary transition-colors">联系我们</a
					>
				</li>
				<li>
					<a
						href="/feedback"
						class="text-gray-600 hover:text-primary transition-colors">意见反馈</a
					>
				</li>
				<li>
					<a
						href="/status"
						class="text-gray-600 hover:text-primary transition-colors">服务状态</a
					>
				</li>
			</ul>
		</div>
	</div>

	<!-- 分隔线 -->
	<div class="border-t border-gray-200 pt-6">
		<!-- 底部链接和版权 -->
		<div
			class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0"
		>
			<div class="flex flex-wrap gap-4 text-sm">
				<a
					href="/en"
					class="text-gray-500 hover:text-gray-700 transition-colors"
					title="English Version"
				>
					English Version
				</a>
				<a
					href="/privacy-policy"
					class="text-gray-500 hover:text-gray-700 transition-colors"
					title="隐私政策"
				>
					隐私政策
				</a>
				<a
					href="/terms"
					class="text-gray-500 hover:text-gray-700 transition-colors"
					title="服务条款"
				>
					服务条款
				</a>
				<a
					href="/sitemap.xml"
					class="text-gray-500 hover:text-gray-700 transition-colors"
					title="网站地图"
				>
					网站地图
				</a>
			</div>
			<p class="text-gray-500 text-sm">© 2025 FlowAI. All rights reserved.</p>
		</div>
	</div>
</div>
